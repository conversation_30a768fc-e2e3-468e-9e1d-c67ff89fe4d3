/* Global styles */
:root {
  --primary: #76c7c0;
  --primary-dark: #5aa9a4;
  --text-light: #e0e0e0;
  --text-dark: #0f2027;
  --bg-dark: #121212;
  --bg-card: #1e1e1e;
}

body {
  margin: 0;
  font-family: 'Inter', 'Segoe UI', 'Roboto', sans-serif;
  background: linear-gradient(135deg, #0f2027, #203a43, #2c5364);
  color: var(--text-light);
  line-height: 1.6;
}

/* Section styles */
main section {
  padding: 80px 5%;
  scroll-margin-top: 80px;
}

.section-divider {
  height: 1px;
  background: linear-gradient(to right, transparent, var(--primary), transparent);
  margin: 0 auto;
  width: 80%;
}

.navbar a.active {
  color: var(--primary);
  background-color: rgba(118, 199, 192, 0.1);
}

/* Scroll behavior */
html {
  scroll-behavior: smooth;
}

/* Enhanced Hero styles */
.hero {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 5%;
}

.hero-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 1200px;
  width: 100%;
  gap: 50px;
}

.hero .info {
  flex: 1;
  text-align: left;
}

.hero .greeting {
  color: var(--primary);
  font-size: 1.2rem;
  font-weight: 500;
  display: block;
  margin-bottom: 10px;
}

.hero h1 {
  font-size: 4rem;
  margin: 0;
  background: linear-gradient(to right, #ffffff, var(--primary));
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.hero .title {
  font-size: 2rem;
  margin: 10px 0 20px;
  color: #a0a0a0;
}

.hero p {
  font-size: 1.2rem;
  line-height: 1.6;
  margin-bottom: 30px;
}

.cta-buttons {
  display: flex;
  gap: 15px;
  margin-top: 25px;
}

.primary-btn, .secondary-btn {
  padding: 12px 25px;
  border-radius: 30px;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
}

.primary-btn {
  background: var(--primary);
  color: var(--text-dark);
}

.primary-btn:hover {
  background: var(--primary-dark);
  transform: translateY(-3px);
  box-shadow: 0 10px 20px rgba(0,0,0,0.2);
}

.secondary-btn {
  background: transparent;
  color: var(--text-light);
  border: 2px solid var(--primary);
}

.secondary-btn:hover {
  color: var(--primary);
  transform: translateY(-3px);
  box-shadow: 0 10px 20px rgba(0,0,0,0.1);
}

.profile-container {
  flex: 0.8;
  position: relative;
  display: flex;
  justify-content: center;
}

.profile-image {
  width: 320px;
  height: 320px;
  object-fit: cover;
  border-radius: 50%;
  border: 4px solid var(--primary);
  position: relative;
  z-index: 2;
}

.profile-background {
  position: absolute;
  width: 320px;
  height: 320px;
  border-radius: 50%;
  background: rgba(118, 199, 192, 0.3);
  top: 15px;
  left: 15px;
  z-index: 1;
}

@media (max-width: 768px) {
  .hero-content {
    flex-direction: column;
    text-align: center;
    padding-top: 100px;
  }
  
  .hero .info {
    text-align: center;
    margin-bottom: 50px;
  }
  
  .cta-buttons {
    justify-content: center;
  }
  
  .profile-container {
    margin-bottom: 30px;
  }
}

h2 {
  font-size: 2.5rem;
  margin-bottom: 40px;
  text-align: center;
  background: linear-gradient(to right, #ffffff, var(--primary));
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  display: inline-block;
}

/* Navbar styles */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: rgba(15, 32, 39, 0.9);
  backdrop-filter: blur(10px);
  z-index: 1000;
  padding: 15px 5%;
}

.navbar ul {
  display: flex;
  justify-content: center;
  list-style: none;
  margin: 0;
  padding: 0;
}

.navbar li {
  margin: 0 15px;
}

.navbar a {
  color: var(--text-light);
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s;
  padding: 5px 10px;
  border-radius: 4px;
}

.navbar a:hover {
  color: var(--primary);
}

/* About section */
.about-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 50px;
  max-width: 1200px;
  margin: 0 auto;
}

.about-text {
  flex: 1;
  text-align: left;
}

.about-text p {
  font-size: 1.1rem;
  margin-bottom: 20px;
}

.skills-container {
  flex: 1;
}

.skill-tag {
  display: inline-block;
  background: rgba(118, 199, 192, 0.2);
  color: var(--primary);
  padding: 8px 16px;
  margin: 5px;
  border-radius: 20px;
  font-size: 0.9rem;
  border: 1px solid var(--primary);
}

/* Services section */
.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  max-width: 1200px;
  margin: 0 auto;
}

.service-card {
  background: var(--bg-card);
  border-radius: 10px;
  padding: 30px;
  transition: transform 0.3s, box-shadow 0.3s;
  border: 1px solid rgba(118, 199, 192, 0.1);
}

.service-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  border-color: var(--primary);
}

.service-card h3 {
  color: var(--primary);
  margin-top: 0;
  font-size: 1.5rem;
}

/* Projects section */
.projects-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  max-width: 1200px;
  margin: 0 auto;
}

.project-card {
  background: var(--bg-card);
  border-radius: 10px;
  padding: 25px;
  transition: transform 0.3s, box-shadow 0.3s;
  border: 1px solid rgba(118, 199, 192, 0.1);
  height: 100%;
}

.project-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  border-color: var(--primary);
}

.project-card h3 {
  margin-top: 0;
}

.project-card a {
  color: var(--primary);
  text-decoration: none;
}

.project-card a:hover {
  text-decoration: underline;
}

/* Contact section */
.contact-container {
  max-width: 800px;
  margin: 0 auto;
}

.contact-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.contact-form input,
.contact-form textarea {
  padding: 15px;
  border-radius: 8px;
  background: var(--bg-card);
  border: 1px solid rgba(118, 199, 192, 0.3);
  color: var(--text-light);
  font-size: 1rem;
}

.contact-form input:focus,
.contact-form textarea:focus {
  outline: none;
  border-color: var(--primary);
}

.contact-form button {
  background: var(--primary);
  color: var(--text-dark);
  border: none;
  padding: 15px 30px;
  border-radius: 30px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  align-self: flex-start;
}

.contact-form button:hover {
  background: var(--primary-dark);
  transform: translateY(-3px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .about-container {
    flex-direction: column;
  }
  
  .navbar ul {
    flex-wrap: wrap;
  }
  
  .navbar li {
    margin: 5px;
  }
  
  .contact-form button {
    align-self: center;
  }
}

/* Projects section */
.projects-container {
  display: flex;
  flex-wrap: wrap;
  gap: 25px;
  justify-content: center;
  max-width: 1200px;
  margin: 0 auto;
}

.project-card {
  background: var(--bg-card);
  border-radius: 10px;
  width: 300px;
  height: 250px;
  overflow: hidden;
  position: relative;
  border: 1px solid rgba(118, 199, 192, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

/* Floating animation */
.project-card.floating {
  animation: floating 3s ease-in-out infinite;
}

.project-card:nth-child(odd) {
  animation-delay: 0.5s;
}

.project-card:nth-child(3n) {
  animation-delay: 1s;
}

@keyframes floating {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-15px);
  }
  100% {
    transform: translateY(0px);
  }
}

.project-card:hover {
  transform: translateY(-10px) scale(1.03);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
  border-color: var(--primary);
  animation-play-state: paused;
}

.project-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 20px;
}

.project-title {
  margin-top: 0;
  margin-bottom: 15px;
  color: var(--primary);
  font-size: 1.4rem;
  transition: transform 0.3s ease;
}

.project-details {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(to top, var(--bg-card), rgba(30, 30, 30, 0.9));
  padding: 20px;
  transform: translateY(100%);
  transition: transform 0.3s ease;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.project-card:hover .project-details {
  transform: translateY(0);
}

.project-card:hover .project-title {
  transform: translateY(-50px);
  opacity: 0;
}

.project-meta {
  display: flex;
  gap: 15px;
  margin-top: 10px;
}

.project-language {
  background: rgba(118, 199, 192, 0.2);
  color: var(--primary);
  padding: 3px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
}

.project-stars {
  font-size: 0.8rem;
  display: flex;
  align-items: center;
  gap: 4px;
}

.project-link {
  display: inline-block;
  margin-top: 10px;
  background: var(--primary);
  color: var(--text-dark);
  text-decoration: none;
  padding: 8px 15px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
  text-align: center;
  transition: background 0.3s ease, transform 0.3s ease;
}

.project-link:hover {
  background: var(--primary-dark);
  transform: translateY(-3px);
}

/* Loading indicator */
.loading {
  text-align: center;
  font-size: 1.2rem;
  color: var(--primary);
  margin: 50px 0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .projects-container {
    flex-direction: column;
    align-items: center;
  }
  
  .project-card {
    width: 100%;
    max-width: 350px;
  }
}

/* Enhanced Services Section Styles */
.services-section {
  max-width: 1200px;
  margin: 0 auto;
}

.section-subtitle {
  font-size: 1.1rem;
  color: var(--text-light);
  opacity: 0.8;
  margin-bottom: 40px;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  text-align: center;
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
  max-width: 1200px;
  margin: 0 auto;
}

.service-card {
  background: var(--bg-card);
  border-radius: 10px;
  padding: 25px;
  transition: transform 0.3s ease, box-shadow 0.3s ease, border-color 0.3s ease;
  border: 1px solid rgba(118, 199, 192, 0.1);
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  position: relative;
  height: 180px;
  overflow: hidden;
}

.service-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
  border-color: var(--primary);
  height: auto;
}

.service-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  margin-bottom: 15px;
  transition: transform 0.3s ease;
}

.service-card:hover .service-icon {
  transform: scale(1.1);
}

.service-title {
  color: var(--primary);
  margin: 0 0 10px 0;
  font-size: 1.4rem;
  font-weight: 600;
  transition: transform 0.3s ease;
}

.service-card:hover .service-title {
  transform: translateY(-5px);
}

.service-description {
  opacity: 0;
  max-height: 0;
  transition: opacity 0.3s ease, max-height 0.5s ease;
  overflow: hidden;
}

.service-card:hover .service-description {
  opacity: 1;
  max-height: 300px;
  margin-top: 10px;
}

.service-description p {
  color: var(--text-light);
  opacity: 0.9;
  margin: 0;
  line-height: 1.6;
  font-size: 0.95rem;
}

@media (max-width: 768px) {
  .services-grid {
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 25px;
  }
  
  .service-card {
    height: 160px;
  }
}

/* Contact form styles */
.form-group {
  position: relative;
  margin-bottom: 20px;
  width: 100%;
}

.contact-form input,
.contact-form textarea {
  width: 100%;
  padding: 15px;
  border-radius: 8px;
  background: var(--bg-card);
  border: 1px solid rgba(118, 199, 192, 0.3);
  color: var(--text-light);
  font-size: 1rem;
  transition: border-color 0.3s, box-shadow 0.3s;
}

.contact-form input:focus,
.contact-form textarea:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 2px rgba(118, 199, 192, 0.2);
}

.contact-form input.error,
.contact-form textarea.error {
  border-color: #ff6b6b;
}

.error-message {
  color: #ff6b6b;
  font-size: 0.85rem;
  position: absolute;
  bottom: -20px;
  left: 0;
}

.contact-form button {
  background: var(--primary);
  color: var(--text-dark);
  border: none;
  padding: 15px 30px;
  border-radius: 30px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  align-self: flex-start;
  position: relative;
}

.contact-form button:hover:not(:disabled) {
  background: var(--primary-dark);
  transform: translateY(-3px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

.contact-form button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.contact-form button.loading {
  padding-left: 45px;
}

.contact-form button.loading:before {
  content: '';
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  border: 2px solid rgba(0, 0, 0, 0.1);
  border-top-color: var(--text-dark);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: translateY(-50%) rotate(360deg); }
}

.form-status {
  margin-top: 20px;
  padding: 10px 15px;
  border-radius: 8px;
  font-weight: 500;
}

.form-status.success {
  background-color: rgba(46, 213, 115, 0.15);
  color: #2ed573;
  border: 1px solid rgba(46, 213, 115, 0.3);
}

.form-status.error {
  background-color: rgba(255, 107, 107, 0.15);
  color: #ff6b6b;
  border: 1px solid rgba(255, 107, 107, 0.3);
}

.form-status.loading {
  background-color: rgba(118, 199, 192, 0.15);
  color: var(--primary);
  border: 1px solid rgba(118, 199, 192, 0.3);
}

/* Modern Navbar styles */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: rgba(15, 32, 39, 0.8);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  z-index: 1000;
  padding: 0;
  transition: all 0.3s ease;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.navbar.scrolled {
  background: rgba(15, 32, 39, 0.95);
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
}

.navbar-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 5%;
  max-width: 1400px;
  margin: 0 auto;
  height: 70px;
}

.navbar-logo {
  font-size: 1.8rem;
  font-weight: 700;
}

.logo-text {
  background: linear-gradient(to right, #ffffff, var(--primary));
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: 800;
}

.navbar-links {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 10px;
}

.navbar-links li {
  position: relative;
  margin: 0;
  padding: 0 10px;
}

.navbar-links a {
  color: var(--text-light);
  text-decoration: none;
  font-weight: 500;
  padding: 8px 12px;
  border-radius: 6px;
  transition: all 0.3s ease;
  display: block;
  position: relative;
}

.navbar-links a:hover {
  color: var(--primary);
  background: rgba(118, 199, 192, 0.05);
}

.navbar-links a.active {
  color: var(--primary);
  font-weight: 600;
}

.nav-indicator {
  position: absolute;
  bottom: -2px;
  left: 50%;
  transform: translateX(-50%);
  height: 3px;
  width: 20px;
  background: var(--primary);
  border-radius: 3px;
  transition: all 0.3s ease;
}

.mobile-menu-toggle {
  display: none;
  color: var(--text-light);
  font-size: 1.5rem;
  cursor: pointer;
  transition: color 0.3s ease;
}

.mobile-menu-toggle:hover {
  color: var(--primary);
}

/* Mobile responsive styles */
@media (max-width: 768px) {
  .mobile-menu-toggle {
    display: block;
  }
  
  .navbar-links {
    position: fixed;
    top: 70px;
    left: 0;
    right: 0;
    flex-direction: column;
    background: rgba(15, 32, 39, 0.95);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    padding: 20px 0;
    gap: 0;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
    transform: translateY(-150%);
    opacity: 0;
    transition: all 0.3s ease;
    z-index: 999;
  }
  
  .navbar-links.active {
    transform: translateY(0);
    opacity: 1;
  }
  
  .navbar-links li {
    width: 100%;
    text-align: center;
    padding: 0;
  }
  
  .navbar-links a {
    padding: 15px;
    width: 100%;
    border-radius: 0;
  }
  
  .nav-indicator {
    display: none;
  }
  
  .navbar-links a.active {
    background: rgba(118, 199, 192, 0.1);
  }
}